'use client';

import React, { useEffect, useState } from 'react';
import { useController } from '@/context/ControllerContext';

interface GBAControllerProps {
  runner?: string;
}

export default function GBAController({ runner }: GBAControllerProps) {
  // 所有hooks必须在组件顶层调用，不能在条件语句之后
  const { isVisible, simulateKeyEvent } = useController();
  const [isMounted, setIsMounted] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);

  // Ensure component is mounted on client side
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // 检测全屏状态
  useEffect(() => {
    const handleFullscreenChange = () => {
      try {
        setIsFullscreen(!!document.fullscreenElement);
      } catch (error) {
        console.warn('Error detecting fullscreen state:', error);
      }
    };

    if (typeof document !== 'undefined') {
      document.addEventListener('fullscreenchange', handleFullscreenChange);
      return () => document.removeEventListener('fullscreenchange', handleFullscreenChange);
    }
  }, []);

  // 条件返回必须在所有hooks之后
  if (!isMounted || !isVisible || runner !== 'EMULATOR_GBA') return null;

  const handlePress = (keyCode: number) => {
    try {
      simulateKeyEvent(keyCode, 'keydown');
    } catch (error) {
      console.error('Error in handlePress:', error);
    }
  };

  const handleRelease = (keyCode: number) => {
    try {
      simulateKeyEvent(keyCode, 'keyup');
    } catch (error) {
      console.error('Error in handleRelease:', error);
    }
  };

  const buttonProps = (keyCode: number) => ({
    onTouchStart: (e: React.TouchEvent) => {
      e.preventDefault();
      handlePress(keyCode);
    },
    onTouchEnd: (e: React.TouchEvent) => {
      e.preventDefault();
      handleRelease(keyCode);
    },
    onMouseDown: (e: React.MouseEvent) => {
      e.preventDefault();
      handlePress(keyCode);
    },
    onMouseUp: (e: React.MouseEvent) => {
      e.preventDefault();
      handleRelease(keyCode);
    },
    onMouseLeave: (e: React.MouseEvent) => {
      e.preventDefault();
      handleRelease(keyCode);
    }
  });

  const containerClass = isFullscreen
    ? 'absolute bottom-0 left-0 right-0 z-50 bg-black bg-opacity-90 p-4'
    : 'fixed bottom-4 left-1/2 transform -translate-x-1/2 z-[9999] w-full max-w-lg px-4';

  return (
    <div className={containerClass}>
      <div className={`
        ${isFullscreen
          ? 'flex items-center justify-center max-w-4xl mx-auto bg-black bg-opacity-95'
          : 'bg-black bg-opacity-80 backdrop-blur-sm rounded-lg'
        }
        p-3 sm:p-4 flex items-center justify-between gap-3 sm:gap-6 w-full
      `}>
        {/* L Button */}
        <button
          {...buttonProps(81)}
          className="px-2 py-1 sm:px-3 sm:py-1 bg-gray-700 hover:bg-gray-600 active:bg-purple-500 text-white rounded text-xs font-bold"
        >
          L
        </button>

        {/* D-Pad */}
        <div className="grid grid-cols-3 gap-1">
          <div />
          <button
            {...buttonProps(38)}
            className="w-8 h-8 sm:w-10 sm:h-10 bg-gray-700 hover:bg-gray-600 active:bg-blue-500 text-white rounded flex items-center justify-center font-bold text-sm sm:text-base"
          >
            ↑
          </button>
          <div />
          <button
            {...buttonProps(37)}
            className="w-8 h-8 sm:w-10 sm:h-10 bg-gray-700 hover:bg-gray-600 active:bg-blue-500 text-white rounded flex items-center justify-center font-bold text-sm sm:text-base"
          >
            ←
          </button>
          <div className="w-8 h-8 sm:w-10 sm:h-10" />
          <button
            {...buttonProps(39)}
            className="w-8 h-8 sm:w-10 sm:h-10 bg-gray-700 hover:bg-gray-600 active:bg-blue-500 text-white rounded flex items-center justify-center font-bold text-sm sm:text-base"
          >
            →
          </button>
          <div />
          <button
            {...buttonProps(40)}
            className="w-8 h-8 sm:w-10 sm:h-10 bg-gray-700 hover:bg-gray-600 active:bg-blue-500 text-white rounded flex items-center justify-center font-bold text-sm sm:text-base"
          >
            ↓
          </button>
          <div />
        </div>

        {/* Select/Start */}
        <div className="flex flex-col gap-2">
          <button
            {...buttonProps(17)}
            className="px-2 py-1 sm:px-3 sm:py-1 bg-gray-700 hover:bg-gray-600 active:bg-blue-500 text-white rounded text-xs font-bold"
          >
            SELECT
          </button>
          <button
            {...buttonProps(13)}
            className="px-2 py-1 sm:px-3 sm:py-1 bg-gray-700 hover:bg-gray-600 active:bg-blue-500 text-white rounded text-xs font-bold"
          >
            START
          </button>
        </div>

        {/* Action buttons */}
        <div className="flex gap-2 sm:gap-3">
          <button
            {...buttonProps(90)}
            className="w-10 h-10 sm:w-12 sm:h-12 bg-gray-700 hover:bg-gray-600 active:bg-red-500 text-white rounded-full flex items-center justify-center font-bold text-sm sm:text-base"
          >
            B
          </button>
          <button
            {...buttonProps(88)}
            className="w-10 h-10 sm:w-12 sm:h-12 bg-gray-700 hover:bg-gray-600 active:bg-red-500 text-white rounded-full flex items-center justify-center font-bold text-sm sm:text-base"
          >
            A
          </button>
        </div>

        {/* R Button */}
        <button
          {...buttonProps(87)}
          className="px-2 py-1 sm:px-3 sm:py-1 bg-gray-700 hover:bg-gray-600 active:bg-purple-500 text-white rounded text-xs font-bold"
        >
          R
        </button>
      </div>
    </div>
  );
}